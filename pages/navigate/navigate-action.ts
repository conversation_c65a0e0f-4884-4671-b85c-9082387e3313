import { Page } from "@playwright/test";

export class NavigateAction {
  constructor(private page: Page) {}

  //open url
  async openUrl(): Promise<void> {
    await this.page.goto("/" + "login");
    await this.page.waitForTimeout(2000);
    console.log(">>> ENV from process.env =", process.env.ENV);
  }

  async switchLanguage(fromLang: string, toLang: string): Promise<void> {
    const langButton = this.page.getByRole("button", { name: fromLang });

    if (await langButton.isVisible()) {
      await langButton.click();
      await this.page.getByText(toLang).click();
    }
  }

  async clickToProfileIcon(): Promise<void> {
    await this.page.locator("#profile").click();
  }
  //goto user timeline
  async goToTimeLine(): Promise<void> {
    await this.page
      .locator(
        'xpath=//*[text()="Your profile" or text()="Trang cá nhân của bạn"]'
      )
      .click();
  }
  async goToSurvey(): Promise<void> {
    await this.page.locator(".gapo-SideNav-itemLink[href*='/survey']").click();
  }

  async goToCreatedSurvey(): Promise<void> {
    await this.page.goto("/" + "survey-created");
  }

  //accept desktop noti popup
  async acceptNotiPopup(): Promise<void> {
    const confirtmNotiPopup = this.page.getByRole("button", {
      name: "Bật thông báo",
    });
    if (await confirtmNotiPopup.isVisible()) {
      await confirtmNotiPopup.click();
    }
  }

  //accept guide post
  async continueGuidelineOfPost(): Promise<void> {
    const continueGuidelineOfPost = this.page.getByRole("button", {
      name: "Tiếp theo",
    });
    if (await continueGuidelineOfPost.isVisible()) {
      await continueGuidelineOfPost.click();
      await this.page.getByRole("button", { name: "Đã hiểu" }).click();
    }
  }
  async gotoSetting(): Promise<void>{
    await this.page.getByRole('link', { name: 'icon-setting Cài đặt' }).click();
  }

  async clickChangePass(): Promise<void> {
     await this.page.getByRole('link', { name: 'Đổi mật khẩu' }).click();
  }

  async closeNotiPopup(): Promise<void> {
    await this.page.waitForTimeout(3000);
    await this.page.locator("#desktop-notification > button").click();
  }

}