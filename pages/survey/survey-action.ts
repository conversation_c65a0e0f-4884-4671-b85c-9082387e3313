import { Page } from "@playwright/test";

export class SurveyAction {
  constructor(private page: Page) {}

  async createSurveyNoDeadline(participants: string[], title: string) {
    await this.page.getByRole("button", { name: "Tạo kh<PERSON>o sát" }).click();
    await this.page.getByRole("button", { name: "Đoạn văn bản" }).click();
    await this.page.getByRole("button", { name: "<PERSON>hê<PERSON> ngườ<PERSON>" }).click();
    for (const p of participants) {
      await this.page.getByText(p).click();
    }
    await this.page.getByRole("button", { name: "<PERSON><PERSON>" }).click();
    await this.inputSurveyName(title);
    await this.page.getByRole("button", { name: "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> sát" }).click();
  }

  async createSurveyWithDeadline(
    deadlineDate: string,
    participants: string[],
    title: string
  ) {
    await this.page.getByRole("button", { name: "Tạo kh<PERSON> sát" }).click();
    await this.page.locator("#DeadlineWrapper div").nth(2).click();
    await this.page.getByTitle(deadlineDate).locator("div").click();
    await this.page.getByRole("button", { name: "Hoàn thành" }).click();
    await this.page.getByRole("button", { name: "Đoạn văn bản" }).click();
    await this.page.getByRole("button", { name: "Thêm người" }).click();
    for (const p of participants) {
      await this.page.getByText(p).click();
    }
    await this.page.getByRole("button", { name: "Xong" }).click();
    await this.inputSurveyName(title);
    await this.page.waitForTimeout(3000);
    await this.page.getByRole("button", { name: "Gửi khảo sát" }).click();
    await this.page.waitForTimeout(3000);
  }

  private async inputSurveyName(title: string) {
    await this.page.waitForTimeout(1000);
    var titleInput = this.page.getByRole("textbox").first();
    await titleInput.fill("");
    await titleInput.fill(title);
  }

  async closeSuccessPopup() {
    await this.page.getByRole("button", { name: "Đóng" }).click();
    await this.page.waitForTimeout(2000);
  }

  async createRecurringSurvey(participants: string[], title: string) {
    await this.page.getByRole("button", { name: "Tạo khảo sát" }).click();
    await this.page.getByRole("radio", { name: "Định kỳ" }).check();
    await this.editRemindToNoRemind();
    await this.page.getByRole("button", { name: "Đoạn văn bản" }).click();
    await this.page.getByRole("button", { name: "Thêm người" }).click();
    for (const p of participants) {
      await this.page.getByText(p).click();
    }
    await this.page.getByRole("button", { name: "Xong" }).click();
    await this.inputSurveyName(title);
    await this.page.getByRole("button", { name: "Gửi khảo sát" }).click();
  }

  async editFirstSurveyType(newType: string) {
    // await this.page
    //   .locator(
    //     "(//a[contains(@href, '/survey/')])[1]/following::div[1]//button[contains(@class, 'gapo-ActionButton')]"
    //   )
    //   .click();
    await this.page.locator('//button[@aria-haspopup="true"]').first().click();
    await this.page.getByText("Chỉnh sửa", { exact: true }).click();
    await this.page.getByRole("button", { name: "Hàng ngày" }).click();
    await this.page.getByText(newType).click();
    await this.page.getByRole("button", { name: "Sửa khảo sát" }).click();
  }

  async editRemindToNoRemind() {
    await this.page.waitForTimeout(1000);
    await this.page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
    await this.page.getByText('Không nhắc trước').click();
  }

  async deleteFirstSurvey() {
    await this.page.locator('//button[@aria-haspopup="true"]').first().click();
    await this.page
      .getByRole("menuitem", { name: "Xóa" })
      .locator("div")
      .first()
      .click();
    await this.page.getByRole("button", { name: "Xác nhận" }).click();
  }

  async cleanUpSurveys() {}
}
