{"name": "playwright-<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"test-auth-prod": "cross-env ENV=prod playwright test --project=test-prod-chrome-auth", "test-post-prod": "cross-env ENV=prod playwright test --project=test-prod-chrome-post", "test-survey-prod": "cross-env ENV=prod playwright test --project=test-prod-chrome-survey --headed --workers=1", "test-auth-staging": "cross-env ENV=staging playwright test --project=test-staging-chrome-auth", "test-post-staging": "cross-env ENV=staging playwright test --project=test-staging-chrome-post", "test-survey-staging": "cross-env ENV=staging playwright test --project=test-staging-chrome-survey", "test-all-prod": "cross-env ENV=prod playwright test --workers=1 --project=test-prod-chrome-post --project=test-prod-chrome-auth  --project=test-prod-chrome-survey"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.54.2", "@types/node": "^24.0.3", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "npm-run-all": "^4.1.5"}}