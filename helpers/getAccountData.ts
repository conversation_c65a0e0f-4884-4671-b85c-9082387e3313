import fs from "fs";
import path from "path";
import dotenv from "dotenv";

// <PERSON><PERSON><PERSON> bảo load dotenv trước khi đọc ENV
const ENV = process.env.ENV || "staging";

export function getAccountData() {
  const filePath = path.resolve(
    __dirname,
    '../data/accounts/accounts-data.json'
  );
  console.log("🔎 Loading account file:", filePath);

  if (!fs.existsSync(filePath)) {
    throw new Error(
      `❌ Cannot find account file for ENV: ${ENV} at ${filePath}`
    );
  }

  const rawData = fs.readFileSync(filePath, "utf-8");
  return JSON.parse(rawData);
}
