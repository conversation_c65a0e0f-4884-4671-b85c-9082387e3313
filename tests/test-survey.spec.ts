// import { test, expect, Page } from "@playwright/test";
// import { createStorageState } from "./setup/common-setup";
// import { Context } from "vm";
// import { NavigateAction } from "../pages/navigate/navigate-action";

//   let context: Context;
//   let page: Page;
//   let navigateAction: NavigateAction;
//   const ENV = process.env.ENV || "prod";

// test.describe("Test Survey", () => {
//     test.beforeAll(async ({ browser }) => {
//         // Tạo storage chung
//         const storagePath = await createStorageState(
//           browser,
//           ENV,
//           "post_account", // account key trong getAccountData()
//           `${ENV}-survey-storageState.json`
//         );
//         // Mở context từ storage
//         context = await browser.newContext({ storageState: storagePath });
//         page = await context.newPage();
//         navigateAction = new NavigateAction(page);
//       });

//   test.afterAll(async () => {
//     await context.close();
//   });

//   test.beforeEach(async () => {
//     await navigateAction.openUrl();
//     await navigateAction.goToSurvey();
//   });

//   test("@createsurvey post text successfully", async () => {
//     await page.getByRole('button',{name: 'Tạo khảo sát'}).click();
//   });

//   test('Case 1: Tạo khảo sát 1 lần không thời hạn', async ({ page }) => {
//     await page.getByRole('button', { name: 'Tạo khảo sát' }).click();
//     await page.getByRole('button', { name: 'Đoạn văn bản' }).click();
//     await page.getByRole('button', { name: 'Thêm người' }).click();
//     await page.getByText('Trần Thị Huyền').click();
//     await page.locator('#desktop-notification').getByRole('button').filter({ hasText: /^$/ }).click();
//     await page.getByRole('button', { name: 'Xong' }).click();
//     await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
//     // await page.getByRole('button', { name: 'Đóng' }).click();
//     // console.log('Case 1: Tạo khảo sát 1 lần không thời hạn: PASS ✅ ');
//     await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
//     await page.getByRole('button', { name: 'Xem khảo sát' }).click();
//     await page.waitForTimeout(3000);
//   });

//   test('Case 2: Tạo khảo sát 1 lần có thời hạn', async ({ page }) => {
//     await page.getByRole('button', { name: 'Tạo khảo sát' }).click();
//     await page.locator('#DeadlineWrapper div').nth(2).click();
//     await page.getByTitle(getCurrentDateTitle()).locator('div').click();
//     // await page.getByTitle('-10-05').locator('div').click();
//     await page.getByRole('button', { name: 'Hoàn thành' }).click();
//     await page.getByRole('button', { name: 'Đoạn văn bản' }).click();
//     await page.getByRole('button', { name: 'Thêm người' }).click();
//     await page.getByText('Trần Thị Huyền').click();
//     await page.getByRole('button', { name: 'Xong' }).click();
//     await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
//     // await page.getByRole('button', { name: 'Đóng' }).click();
//     await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
//     await page.getByRole('button', { name: 'Xem khảo sát' }).click();
//     await page.waitForTimeout(3000);
//   });

//   test('Case 3: Tạo khảo sát định kỳ', async ({ page }) => {
//     await page.getByRole('button', { name: 'Tạo khảo sát' }).click();
//     await page.getByRole('radio', { name: 'Định kỳ' }).check();
//     await page.getByRole('button', { name: 'Đoạn văn bản' }).click();
//     await page.getByRole('button', { name: 'Thêm người' }).click();
//     await page.getByText('Trần Thị Huyền').click();
//     await page.locator('#desktop-notification').getByRole('button').filter({ hasText: /^$/ }).click();
//     await page.getByRole('button', { name: 'Xong' }).click();
//     await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
//     await page.getByText('Đóng').click();
//     await page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
//     await page.getByText('Không nhắc trước').click();
//     await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
//     // await page.getByRole('button', { name: 'Đóng' }).click();
//     await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
//     await page.getByRole('button', { name: 'Xem khảo sát' }).click();
//     await page.waitForTimeout(3000);
//   });

//   test('Case 4: Chỉnh sửa khảo sát thành có thời hạn', async ({ page }) => {
//     await page.locator("(//a[contains(@href, '/survey/')])[1]/following::div[1]//button[contains(@class, 'gapo-ActionButton')]").click();
//     await page.getByText('Chỉnh sửa', { exact: true }).click();
//     await page.getByRole('button', { name: 'Hàng ngày' }).click();
//     await page.getByText('Hàng tuần').click();
//     await page.getByRole('button', { name: 'Sửa khảo sát' }).click();
//     // await page.getByRole('button', { name: 'Đóng' }).click();
//     await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
//     await page.getByRole('button', { name: 'Xem khảo sát' }).click();
//     await page.waitForTimeout(3000);
//   });

//   test('Case 5: Xóa khảo sát', async ({ page }) => {
//     await page.locator('//button[@aria-haspopup="true"]').first().click();
//     await page.getByRole("menuitem", { name: "Xóa" }).locator("div").first().click();
//     await page.waitForTimeout(1000);
//     await page.getByRole("button", { name: "Xác nhận" }).click();
//     await page.waitForTimeout(1000);
//   });
// });
import { Page, test } from "@playwright/test";
import { createStorageState } from "./setup/common-setup";
import { Context } from "vm";
import { NavigateAction } from "../pages/navigate/navigate-action";
import { SurveyAction } from "../pages/survey/survey-action";
import { SurveyVerify } from "../pages/survey/survey-verify";
import surveyData from "../data/survey-data.json";

let page: Page;
let context: Context;
let nav: NavigateAction;
let surveyAction: SurveyAction;
let surveyVerify: SurveyVerify;
const ENV = process.env.ENV || "prod";

test.describe("Survey Tests", () => {
  test.beforeAll(async ({ browser }) => {
    // Tạo storage chung
    const storagePath = await createStorageState(
      browser,
      ENV,
      "survey_account", // account key trong getAccountData()
      `${ENV}-survey-storage-state.json`
    );

    // Mở context từ storage
    context = await browser.newContext({ storageState: storagePath });
    page = await context.newPage();
    nav = new NavigateAction(page);
  });

  test.afterAll(async () => {
    await context.close();
  });

  test.beforeEach(async () => {
    await nav.openUrl();
    await nav.goToSurvey();
    await nav.closeNotiPopup();
    surveyAction = new SurveyAction(page);
    surveyVerify = new SurveyVerify(page);
  });
  // test("@createsurvey with deadline", async () => {
  //   await surveyAction.createSurveyWithDeadline(
  //     surveyData.case2.deadlineDate,
  //     surveyData.case2.participants,
  //     surveyData.new_survey.title
  //   );
  //   await surveyVerify.verifySurveyVisible();
  // });

  test("@createsurvey no deadline", async () => {
    await surveyAction.createSurveyNoDeadline(
      surveyData.case1.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyVisible();
    await surveyAction.closeSuccessPopup();
  });

  test("@createsurveyWithDeadline with deadline", async () => {
    await surveyAction.createSurveyWithDeadline(
      surveyData.case2.deadlineDate,
      surveyData.case2.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyVisible();
    await surveyAction.closeSuccessPopup();
  });

  test("@createsurveyRecurring recurring survey", async () => {
    await surveyAction.createRecurringSurvey(
      surveyData.case3.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyVisible();
    await surveyAction.closeSuccessPopup();
  });

  test("@editSurvey change type", async () => {
    await surveyAction.createRecurringSurvey(
      surveyData.case4.participants,
      surveyData.new_survey.title
    );
    await surveyAction.closeSuccessPopup();
    await surveyAction.editFirstSurveyType(surveyData.case4.newType);
    await surveyVerify.verifySurveyVisible();
  });

  test.only("@deleteSurvey delete first survey", async () => {
    await nav.goToCreatedSurvey();
    await surveyAction.deleteFirstSurvey();
    await surveyVerify.verifySurveyDeleted();
  });

  test.afterEach(async () => {
    if (test.info().title.includes("delete")) return;
    await nav.goToCreatedSurvey();
    // await surveyAction.cleanUpSurveys();
    await surveyAction.deleteFirstSurvey();
  });
});
